import React, { useEffect, useState } from "react";
import axios from "axios";
import { Link } from "react-router-dom";

const FileList = () => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    axios
      .get("http://localhost:8080/files/all")
      .then((response) => {
        setFiles(response.data);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching files:", error);
        setLoading(false);
      });
  }, []);

  return (
    <div className="min-h-screen w-full bg-gray-100 flex flex-col items-center p-6">
      <div className="relative overflow-x-auto shadow-md sm:rounded-lg w-full bg-white p-4">
        <h2 className="text-2xl font-bold text-gray-700 text-center mb-6">
          Uploaded Files
        </h2>

        {loading ? (
          <p className="text-center text-gray-500">Loading files...</p>
        ) : files.length > 0 ? (
          <table className="w-full text-sm text-left text-gray-500 border border-gray-200">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50 border-b border-gray-300">
              <tr>
                <th className="px-6 py-3 whitespace-nowrap">S. No.</th>
                <th className="px-6 py-3">File Title</th>
                <th className="px-6 py-3 whitespace-nowrap">File Owner</th>
                <th className="px-6 py-3 whitespace-nowrap">Created Date</th>
                <th className="px-6 py-3 text-right">Action</th>
              </tr>
            </thead>
            <tbody className="w-full">
              {files.map((file, index) => (
                <tr
                  key={file.id}
                  className="bg-white border-b border-gray-200 hover:bg-gray-50"
                >
                  <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
                    {index + 1}
                  </td>
                  <td className="px-6 py-4 text-nowrap truncate w-1/3 max-w-xs">
                    {file.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {file.user.firstName + " " + file.user.lastName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {file.createdDate
                      ? new Date(file.createdDate).toLocaleDateString("en-GB")
                      : "N/A"}
                  </td>
                  <td className="px-6 py-4 text-right">
                    <Link
                      to={`/file/${file.id}/${file.title}`}
                      className="font-medium text-blue-600 hover:underline"
                    >
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-center text-gray-500">No files available</p>
        )}
      </div>
    </div>
  );
};

export default FileList;
